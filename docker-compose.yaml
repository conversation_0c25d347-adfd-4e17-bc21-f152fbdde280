version: '3.9'

services:
  pilardin:
    build:
      context: .
      args:
        OLLAMA_BASE_URL: '/ollama'
      dockerfile: Dockerfile
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    container_name: Pilardin.RAG
    volumes:
      - Pilardin.RAG:/app/backend/data
    ports:
      - ${PILARDIN_PORT-4000}:8090
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434  # Use service name as hostname
      - TIKA_SERVER_URL=http://tika:9998     # Tika server for document processing
      - PILARDIN_SECRET_KEY=${PILARDIN_SECRET_KEY:-}
    depends_on:
      - ollama
      - tika
    restart: unless-stopped

  ollama:
    image: ollama/ollama
    container_name: Pilardin.Ollama
    ports:
      - 11434:11434
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped

  tika:
    image: apache/tika:latest
    container_name: Pilardin.Tika
    ports:
      - 9998:9998
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9998/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  Pilardin.RAG:
    external: true
  ollama_data:
    driver: local
